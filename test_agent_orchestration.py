#!/usr/bin/env python3
"""Test script for the new agent orchestration architecture."""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.agents.coordinator import CoordinatorAgent
from app.core.logging import logger


async def test_agent_orchestration():
    """Test the agent orchestration functionality."""
    
    print("🚀 Testing Agent Orchestration Architecture")
    print("=" * 50)
    
    try:
        # Initialize the coordinator
        print("1. Initializing CoordinatorAgent...")
        coordinator = CoordinatorAgent()
        print("✅ CoordinatorAgent initialized successfully")
        
        # Test queries
        test_queries = [
            "get all the active task for this month",
            "show me critical tasks",
            "find overdue tasks",
            "what is the weather today"  # This should not be handled directly
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Testing query: '{query}'")
            print("-" * 40)
            
            try:
                # Orchestrate the query
                query_intent = await coordinator.orchestrate_query(
                    query=query,
                    project_id="86"  # Use the same project ID from the curl example
                )
                
                print(f"   Primary Type: {query_intent.primary_type.value}")
                print(f"   Detected Types: {[t.value for t in query_intent.detected_types]}")
                print(f"   Can Handle Directly: {query_intent.can_handle_directly}")
                
                if query_intent.can_handle_directly and query_intent.direct_response:
                    print(f"   Direct Response Length: {len(query_intent.direct_response)} chars")
                    print(f"   Direct Response Preview: {query_intent.direct_response[:200]}...")
                else:
                    print(f"   Analysis Summary: {query_intent.analysis_summary}")
                    print(f"   Filters Generated: {len(query_intent.combined_result.final_filters)}")
                
                print("✅ Query processed successfully")
                
            except Exception as e:
                print(f"❌ Error processing query: {e}")
                logger.error(f"Test query failed: {e}")
        
        # Get performance stats
        print(f"\n📊 Performance Statistics:")
        print("-" * 40)
        stats = coordinator.get_performance_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        print(f"\n🎉 Agent orchestration test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.error(f"Agent orchestration test failed: {e}")
        return False
    
    return True


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(test_agent_orchestration())
    
    if success:
        print("\n✅ All tests passed! The agent orchestration is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed! Check the logs for more details.")
        sys.exit(1)
