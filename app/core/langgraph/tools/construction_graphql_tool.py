"""Construction project GraphQL tool for LangGraph.

This module provides a GraphQL tool that can dynamically interpret natural language
queries and convert them into GraphQL queries to retrieve construction project data
from a Nest.js backend.
"""

import json
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type

from langchain_core.callbacks import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.logging import logger
from .graphql_client import ConstructionScheduleQueryBuilder, GraphQLClient


class ConstructionGraphQLInput(BaseModel):
    """Input schema for construction GraphQL queries."""
    
    query: str = Field(
        description="Natural language query about construction schedules, tasks, or activities. "
                   "Examples: 'Show me all critical tasks', 'Find overdue schedules', 'Get tasks for project X', "
                   "'List priority tasks', 'Show me tasks with delays', 'Find tasks assigned to <PERSON>'"
    )
    project_id: Optional[str] = Field(
        default=None,
        description="Optional specific project ID to filter results"
    )
    limit: Optional[int] = Field(
        default=10,
        description="Maximum number of results to return (default: 10, max: 100)"
    )


class ConstructionGraphQLTool(BaseTool):
    """Tool for querying construction project data via GraphQL."""
    
    name: str = "construction_schedule_query"
    description: str = (
        "Query construction schedule data from the Nest.js backend using natural language. "
        "This tool can retrieve information about schedules, tasks, activities, and project timelines. "
        "Use this tool when you need to get specific construction schedule data, check task status, "
        "find critical path items, delays, progress, or assignment information."
    )
    args_schema: Type[BaseModel] = ConstructionGraphQLInput
    
    def __init__(self, **kwargs):
        """Initialize the GraphQL tool with endpoint configuration."""
        super().__init__(**kwargs)
        # Initialize client and query builder lazily
        self._client: Optional[GraphQLClient] = None
        self._query_builder: Optional[ConstructionScheduleQueryBuilder] = None
    
    @property
    def client(self) -> GraphQLClient:
        """Get or create the GraphQL client."""
        if self._client is None:
            endpoint = getattr(settings, 'GRAPHQL_ENDPOINT', 'http://localhost:3000/graphql')
            self._client = GraphQLClient(endpoint)
        return self._client
    
    @property
    def query_builder(self) -> ConstructionScheduleQueryBuilder:
        """Get or create the query builder."""
        if self._query_builder is None:
            self._query_builder = ConstructionScheduleQueryBuilder()
        return self._query_builder
    
    def _parse_natural_language_query(self, query: str) -> Dict[str, Any]:
        """Parse natural language query to determine what data to fetch.
        
        Args:
            query: Natural language query string
            
        Returns:
            Dict containing query intent and parameters
        """
        query_lower = query.lower()
        
        # Determine query type and filters
        filters = {}
        includes = {}
        
        # Always use schedules as the primary query type
        query_type = 'schedules'
        
        # Status filters
        if 'completed' in query_lower:
            filters['status'] = 'COMPLETED'
        elif 'in progress' in query_lower or 'ongoing' in query_lower or 'active' in query_lower:
            filters['status'] = 'IN_PROGRESS'
        elif 'not started' in query_lower or 'pending' in query_lower:
            filters['status'] = 'NOT_STARTED'
        elif 'overdue' in query_lower:
            filters['status'] = 'OVERDUE'
        
        # Critical path and priority filters
        if 'critical' in query_lower:
            filters['is_critical'] = True
        if 'priority' in query_lower:
            filters['is_priority'] = True
        
        # Progress filters
        if 'no progress' in query_lower or '0%' in query_lower:
            filters['percent_complete_max'] = 0
        elif 'partial' in query_lower or 'in progress' in query_lower:
            filters['percent_complete_min'] = 1
            filters['percent_complete_max'] = 99
        elif 'complete' in query_lower or '100%' in query_lower:
            filters['percent_complete_min'] = 100
        
        # Delay filters
        if 'delay' in query_lower or 'behind' in query_lower:
            filters['has_delays'] = True
        
        # Assignment filters
        assignee_match = re.search(r'assigned to (\w+)', query_lower)
        if assignee_match:
            filters['assignee_name'] = assignee_match.group(1)
        
        # Search text filters
        if 'containing' in query_lower or 'with name' in query_lower or 'called' in query_lower:
            search_match = re.search(r'(?:containing|with name|called)\s+["\']?([^"\']+)["\']?', query_lower)
            if search_match:
                filters['search_text'] = search_match.group(1).strip()
        
        # Date-based filters
        now = datetime.now()
        
        # "this month" or "current month"
        if 'this month' in query_lower or 'current month' in query_lower:
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            # Get next month's first day
            if now.month == 12:
                end_of_month = start_of_month.replace(year=now.year + 1, month=1)
            else:
                end_of_month = start_of_month.replace(month=now.month + 1)

            # For "active tasks this month", we want tasks where:
            # The baseline start date is within this month
            filters['date_filter'] = {
                'current_month': True,
                'start_date': start_of_month.isoformat(),
                'end_date': end_of_month.isoformat(),
                'baseline_start': start_of_month.isoformat(),
                'baseline_finish': end_of_month.isoformat(),
            }
            
        # "this week" or "current week"  
        elif 'this week' in query_lower or 'current week' in query_lower:
            start_of_week = now - timedelta(days=now.weekday())
            start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_week = start_of_week + timedelta(days=7)
            
            filters['date_filter'] = {
                'current_week': True,
                'start_date': start_of_week.isoformat(),
                'end_date': end_of_week.isoformat()
            }
            
        # "today" or "current day"
        elif 'today' in query_lower or 'current day' in query_lower:
            start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_day = start_of_day + timedelta(days=1)
            
            filters['date_filter'] = {
                'current_day': True,
                'start_date': start_of_day.isoformat(),
                'end_date': end_of_day.isoformat()
            }
            
        # "overdue" or "past due"
        elif 'overdue' in query_lower or 'past due' in query_lower:
            filters['date_filter'] = {
                'overdue': True,
                'reference_date': now.isoformat()
            }
        
        # Include related data based on query
        if any(keyword in query_lower for keyword in ['assignee', 'assigned', 'team', 'user']):
            includes['assignees'] = True
        if any(keyword in query_lower for keyword in ['comment', 'note', 'discussion']):
            includes['comments'] = True
        if any(keyword in query_lower for keyword in ['document', 'file', 'attachment']):
            includes['documents'] = True
        if any(keyword in query_lower for keyword in ['media', 'image', 'photo']):
            includes['medias'] = True
        if any(keyword in query_lower for keyword in ['detail', 'full', 'complete', 'all']):
            includes['details'] = True
        
        return {
            'type': query_type,
            'filters': filters,
            'includes': includes
        }
    
    def _build_query(
        self, 
        query_intent: Dict[str, Any], 
        project_id: Optional[str] = None,
        limit: int = 50
    ) -> str:
        """Build GraphQL query based on intent.
        
        Args:
            query_intent: Parsed query intent
            project_id: Optional project ID filter
            limit: Maximum results to return
            
        Returns:
            GraphQL query string
        """
        filters = query_intent['filters']
        includes = query_intent.get('includes', {})
        
        # Add project_id to filters if provided
        if project_id:
            filters['project_id'] = project_id
        
        # Ensure reasonable limit
        limit = min(limit, 100)
        
        # Always use schedules query with appropriate includes
        return self.query_builder.build_schedules_query(
            filter_params=filters if filters else None,
            include_details=includes.get('details', True),
            include_assignees=includes.get('assignees', False),
            include_comments=includes.get('comments', False),
            include_documents=includes.get('documents', False),
            include_medias=includes.get('medias', False),
            limit=limit
        )
    
    def _format_results(self, results: Dict[str, Any], query_type: str, date_filter: Optional[Dict[str, Any]] = None) -> str:
        """Format GraphQL results for display.
        
        Args:
            results: GraphQL query results
            query_type: Type of query (always 'schedules')
            date_filter: Optional date filter for client-side filtering
            
        Returns:
            Formatted string representation of results
        """
        if 'data' not in results:
            return "No data returned from GraphQL query."
        
        data = results['data']
        schedules = data.get('schedules', {})
        
        if not schedules or not schedules.get('nodes'):
            return "No schedules found matching the criteria."
        
        nodes = schedules['nodes']
        page_info = schedules.get('pageInfo', {})
        
        # Apply client-side date filtering if specified
        if date_filter and nodes:
            filtered_nodes = []
            now = datetime.now()
            
            for schedule in nodes:
                # Get date fields
                baseline_start = schedule.get('baselineStart')
                baseline_finish = schedule.get('baselineFinish')
                actual_start = schedule.get('actualStart')
                actual_finish = schedule.get('actualFinish')
                
                # Skip if no date information
                if not (baseline_start or actual_start):
                    continue
                
                # Parse dates
                try:
                    start_date = None
                    finish_date = None
                    
                    if actual_start:
                        start_date = datetime.fromisoformat(actual_start.replace('Z', '+00:00'))
                    elif baseline_start:
                        start_date = datetime.fromisoformat(baseline_start.replace('Z', '+00:00'))
                    
                    if actual_finish:
                        finish_date = datetime.fromisoformat(actual_finish.replace('Z', '+00:00'))
                    elif baseline_finish:
                        finish_date = datetime.fromisoformat(baseline_finish.replace('Z', '+00:00'))
                    
                    # Apply date filters
                    if date_filter.get('current_month'):
                        filter_start = datetime.fromisoformat(date_filter['start_date'])
                        filter_end = datetime.fromisoformat(date_filter['end_date'])

                        # Check if we should filter by baseline start date
                        if date_filter.get('filter_by_baseline_start'):
                            # For "active tasks this month" - filter by baseline start date in this month
                            if baseline_start:
                                try:
                                    baseline_start_date = datetime.fromisoformat(baseline_start.replace('Z', '+00:00'))
                                    # Task is active this month if baseline start is within this month
                                    if filter_start <= baseline_start_date < filter_end:
                                        filtered_nodes.append(schedule)
                                except ValueError:
                                    # Skip if can't parse baseline start date
                                    continue
                        else:
                            # Original logic for tasks that are ongoing/active during this month
                            task_is_active = False

                            # Only consider tasks that are not actually completed
                            if not actual_finish:
                                # Check if baseline dates overlap with this month
                                baseline_start_date = None
                                baseline_finish_date = None

                                try:
                                    if baseline_start:
                                        baseline_start_date = datetime.fromisoformat(baseline_start.replace('Z', '+00:00'))
                                    if baseline_finish:
                                        baseline_finish_date = datetime.fromisoformat(baseline_finish.replace('Z', '+00:00'))
                                except ValueError:
                                    # Skip if can't parse baseline dates
                                    continue

                                # Task overlaps with this month if:
                                # - It starts before the end of this month AND
                                # - It finishes after the start of this month
                                if baseline_start_date and baseline_finish_date:
                                    if (baseline_start_date < filter_end and baseline_finish_date >= filter_start):
                                        task_is_active = True
                                elif baseline_start_date:
                                    # If only start date, check if it's in a reasonable range
                                    if baseline_start_date < filter_end:
                                        task_is_active = True
                                elif baseline_finish_date:
                                    # If only finish date, check if it's after start of month
                                    if baseline_finish_date >= filter_start:
                                        task_is_active = True

                            if task_is_active:
                                filtered_nodes.append(schedule)
                    
                    elif date_filter.get('current_week'):
                        # Similar logic for current week
                        filter_start = datetime.fromisoformat(date_filter['start_date'])
                        filter_end = datetime.fromisoformat(date_filter['end_date'])
                        
                        if start_date and start_date < filter_end:
                            if not finish_date or finish_date >= filter_start:
                                filtered_nodes.append(schedule)
                    
                    elif date_filter.get('current_day'):
                        # Similar logic for current day
                        filter_start = datetime.fromisoformat(date_filter['start_date'])
                        filter_end = datetime.fromisoformat(date_filter['end_date'])
                        
                        if start_date and start_date < filter_end:
                            if not finish_date or finish_date >= filter_start:
                                filtered_nodes.append(schedule)
                    
                    elif date_filter.get('overdue'):
                        # Task is overdue if baseline finish is in the past but task isn't completed
                        if baseline_finish and not actual_finish:
                            baseline_finish_date = datetime.fromisoformat(baseline_finish.replace('Z', '+00:00'))
                            if baseline_finish_date < now:
                                filtered_nodes.append(schedule)
                
                except (ValueError, TypeError) as e:
                    # Skip tasks with invalid date formats
                    logger.warning(f"Invalid date format in schedule {schedule.get('id', 'unknown')}: {e}")
                    continue
            
            nodes = filtered_nodes
            
            # If no tasks match the date filter, provide a helpful message
            if not nodes and date_filter:
                original_count = len(schedules['nodes'])
                if date_filter.get('current_month'):
                    if date_filter.get('filter_by_baseline_start'):
                        return f"No tasks have their baseline start date in July 2025. Found {original_count} total schedule items, but none have baseline start dates in this month."
                    else:
                        return f"No tasks are currently active in July 2025. Found {original_count} total schedule items, but none are active during this month. Most tasks appear to be from previous years or future planned activities."
                elif date_filter.get('current_week'):
                    return f"No tasks are currently active this week. Found {original_count} total schedule items, but none are active during this time period."
                elif date_filter.get('current_day'):
                    return f"No tasks are currently active today. Found {original_count} total schedule items, but none are active today."
        
        # Add context about the time period if date filtering was applied
        if date_filter:
            if date_filter.get('current_month'):
                time_context = "July 2025"
            elif date_filter.get('current_week'):
                time_context = "this week"
            elif date_filter.get('current_day'):
                time_context = "today"
            else:
                time_context = "the specified time period"
            formatted = f"Found {len(nodes)} schedule(s) active during {time_context}"
        else:
            formatted = f"Found {len(nodes)} schedule(s)"
            
        if page_info.get('hasNextPage'):
            formatted += " (more results available)"
        formatted += ":\n\n"
        
        # Add note if showing historical data that spans to current time
        if date_filter and nodes:
            sample_task = nodes[0]
            baseline_start = sample_task.get('baselineStart', '')
            if baseline_start and '2021' in baseline_start:
                formatted += "📋 **Note:** These are long-running project tasks that started in 2021 and extend through the current period.\n\n"
        
        for schedule in nodes:
            formatted += f"**{schedule['name']}** (ID: {schedule['id']})\n"
            
            if schedule.get('wbs'):
                formatted += f"  WBS: {schedule['wbs']}\n"
            
            formatted += f"  Status: {schedule.get('status', 'N/A')}\n"
            formatted += f"  Progress: {schedule.get('percentComplete', 0)}%\n"
            
            if schedule.get('isCritical'):
                formatted += "  🔴 Critical Path Task\n"
            if schedule.get('isPriority'):
                formatted += "  ⭐ Priority Task\n"
            
            # Timeline information
            if schedule.get('baselineStart'):
                formatted += f"  Baseline Start: {schedule['baselineStart']}\n"
            if schedule.get('baselineFinish'):
                formatted += f"  Baseline Finish: {schedule['baselineFinish']}\n"
            if schedule.get('actualStart'):
                formatted += f"  Actual Start: {schedule['actualStart']}\n"
            if schedule.get('actualFinish'):
                formatted += f"  Actual Finish: {schedule['actualFinish']}\n"
            
            # Duration and delays
            if schedule.get('baselineDuration'):
                formatted += f"  Duration: {schedule['baselineDuration']}\n"
            if schedule.get('daysDelayed') and schedule['daysDelayed'] > 0:
                formatted += f"  ⚠️ Delayed: {schedule['daysDelayed']} days\n"
            
            # Variance information
            if schedule.get('startVariance'):
                formatted += f"  Start Variance: {schedule['startVariance']}\n"
            if schedule.get('finishVariance'):
                formatted += f"  Finish Variance: {schedule['finishVariance']}\n"
            
            # Predecessors
            if schedule.get('predecessors'):
                formatted += f"  Predecessors: {schedule['predecessors']}\n"
            
            # Assignees
            if schedule.get('assignees') and schedule['assignees'].get('nodes'):
                assignee_names = [assignee['name'] for assignee in schedule['assignees']['nodes']]
                formatted += f"  Assigned To: {', '.join(assignee_names)}\n"
            
            # Comments
            if schedule.get('commentCount') and schedule['commentCount'] > 0:
                formatted += f"  Comments: {schedule['commentCount']}\n"
            
            # Notes
            if schedule.get('notes'):
                notes_preview = schedule['notes'][:100] + "..." if len(schedule['notes']) > 100 else schedule['notes']
                formatted += f"  Notes: {notes_preview}\n"
            
            # Project information
            if schedule.get('projectId'):
                formatted += f"  Project ID: {schedule['projectId']}\n"
            
            # Outline information
            if schedule.get('outlineLevel'):
                formatted += f"  Outline Level: {schedule['outlineLevel']}\n"
            if schedule.get('outlineNumber'):
                formatted += f"  Outline Number: {schedule['outlineNumber']}\n"
            
            formatted += "\n"
        
        return formatted
    
    def _run(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: Optional[int] = 10,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute the tool synchronously (not implemented)."""
        raise NotImplementedError("This tool only supports async execution")
    
    async def _arun(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: Optional[int] = 50,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> str:
        """Execute the GraphQL query tool asynchronously.
        
        Args:
            query: Natural language query
            project_id: Optional project ID filter
            limit: Maximum results to return
            run_manager: Optional callback manager
            
        Returns:
            Formatted results string
        """
        try:
            # Get schedule_id and auth_token from callback manager metadata if available
            schedule_id = None
            auth_token = None
            if run_manager and hasattr(run_manager, 'metadata') and run_manager.metadata:
                schedule_id = run_manager.metadata.get('schedule_id')
                auth_token = run_manager.metadata.get('auth_token')
            
            # Use provided project_id or schedule_id as fallback
            context_id = project_id or schedule_id
            
            # Parse the natural language query
            query_intent = self._parse_natural_language_query(query)
            logger.info(
                "graphql_query_parsed",
                query=query,
                intent=query_intent,
                project_id=project_id,
                schedule_id=schedule_id,
                context_id=context_id,
                limit=limit
            )
            
            # Build GraphQL query
            graphql_query = self._build_query(query_intent, context_id, limit)
            
            # Prepare variables
            variables = query_intent['filters']
            if limit:
                variables['limit'] = limit
            
            # Execute the GraphQL query
            results = await self.client.execute_query(
                query=graphql_query,
                variables=variables,
                auth_token=auth_token
            )
            
            # Format and return results
            date_filter = query_intent['filters'].get('date_filter')
            formatted_results = self._format_results(results, query_intent['type'], date_filter)
            
            logger.info(
                "graphql_query_executed",
                query_type=query_intent['type'],
                results_count=len(results.get('data', {}).get(query_intent['type'], [])),
                query=query[:100] + "..." if len(query) > 100 else query
            )
            
            return formatted_results
            
        except Exception as e:
            error_msg = f"Error executing GraphQL query: {str(e)}"
            logger.error(
                "graphql_query_error",
                error=str(e),
                query=query[:100] + "..." if len(query) > 100 else query
            )
            return error_msg
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.close()


# Create the tool instance
construction_schedule_tool = ConstructionGraphQLTool()