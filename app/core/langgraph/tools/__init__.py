"""LangGraph tools for enhanced language model capabilities.

This package contains custom tools that can be used with LangGraph to extend
the capabilities of language models. Currently includes tools for web search
and other external integrations.
"""

from langchain_core.tools.base import BaseTool

from .duckduckgo_search import duckduckgo_search_tool
from .construction_graphql_tool import construction_schedule_tool

tools: list[BaseTool] = [construction_schedule_tool, duckduckgo_search_tool]
