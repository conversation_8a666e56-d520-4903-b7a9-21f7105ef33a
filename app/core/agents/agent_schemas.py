"""Pydantic schemas and data models for the agent swarm system.

This module defines the core data structures used for communication between
agents, query parsing, and result synthesis in the construction schedule
agent swarm.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class QueryType(str, Enum):
    """Types of queries that can be processed by agents."""
    
    TEMPORAL = "temporal"
    ASSIGNMENT = "assignment"
    CRITICAL_PATH = "critical_path"
    PROGRESS = "progress"
    FILTER = "filter"
    SEARCH = "search"


class AgentConfidence(BaseModel):
    """Confidence score and reasoning for an agent's analysis."""
    
    score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0,
        description="Confidence score between 0 and 1"
    )
    reasoning: str = Field(
        ...,
        description="Human-readable explanation of confidence score"
    )
    matched_patterns: List[str] = Field(
        default_factory=list,
        description="List of patterns that matched in the query"
    )


class TemporalContext(BaseModel):
    """Context for time-based queries."""
    
    date_type: Optional[str] = Field(
        None,
        description="Type of date query (current_month, this_week, overdue, etc.)"
    )
    start_date: Optional[datetime] = Field(
        None,
        description="Start date for the temporal filter"
    )
    end_date: Optional[datetime] = Field(
        None,
        description="End date for the temporal filter"
    )
    reference_date: Optional[datetime] = Field(
        None,
        description="Reference date for relative queries (e.g., 'overdue' uses current time)"
    )
    timeline_field: Optional[str] = Field(
        None,
        description="Which timeline field to use (baselineStart, actualStart, etc.)"
    )


class AssignmentContext(BaseModel):
    """Context for assignment-based queries."""
    
    assignee_names: List[str] = Field(
        default_factory=list,
        description="List of assignee names mentioned in query"
    )
    is_unassigned: bool = Field(
        default=False,
        description="Whether query is looking for unassigned tasks"
    )
    team_query: bool = Field(
        default=False,
        description="Whether query involves team/group assignments"
    )


class CriticalContext(BaseModel):
    """Context for critical path and priority queries."""
    
    is_critical: Optional[bool] = Field(
        None,
        description="Whether to filter for critical path tasks"
    )
    is_priority: Optional[bool] = Field(
        None,
        description="Whether to filter for priority tasks"
    )
    dependency_type: Optional[str] = Field(
        None,
        description="Type of dependency query (blocking, bottleneck, etc.)"
    )


class ProgressContext(BaseModel):
    """Context for progress and status-based queries."""
    
    status_filter: Optional[str] = Field(
        None,
        description="Status to filter by (COMPLETED, IN_PROGRESS, etc.)"
    )
    completion_min: Optional[int] = Field(
        None,
        ge=0,
        le=100,
        description="Minimum completion percentage"
    )
    completion_max: Optional[int] = Field(
        None,
        ge=0,
        le=100,
        description="Maximum completion percentage"
    )
    has_delays: Optional[bool] = Field(
        None,
        description="Whether to filter for delayed tasks"
    )


class FilterContext(BaseModel):
    """Context for general filtering and search queries."""
    
    project_id: Optional[str] = Field(
        None,
        description="Project ID to filter by"
    )
    search_text: Optional[str] = Field(
        None,
        description="Text to search for in task names/descriptions"
    )
    limit: Optional[int] = Field(
        None,
        ge=1,
        le=100,
        description="Maximum number of results to return"
    )
    sort_field: Optional[str] = Field(
        None,
        description="Field to sort results by"
    )
    sort_direction: Optional[str] = Field(
        "ASC",
        description="Sort direction (ASC or DESC)"
    )


class AgentContext(BaseModel):
    """Combined context from all agent types."""
    
    temporal: Optional[TemporalContext] = None
    assignment: Optional[AssignmentContext] = None
    critical: Optional[CriticalContext] = None
    progress: Optional[ProgressContext] = None
    filter: Optional[FilterContext] = None


class GraphQLFilter(BaseModel):
    """Structured GraphQL filter specification."""
    
    field: str = Field(..., description="GraphQL field name")
    operator: str = Field(..., description="Filter operator (eq, gt, like, etc.)")
    value: Any = Field(..., description="Filter value")
    nested_path: Optional[List[str]] = Field(
        None,
        description="Path for nested field access (e.g., ['assignees', 'name'])"
    )


class AgentResult(BaseModel):
    """Result from an individual agent's analysis."""
    
    agent_type: QueryType = Field(
        ...,
        description="Type of agent that produced this result"
    )
    confidence: AgentConfidence = Field(
        ...,
        description="Confidence score and reasoning"
    )
    filters: List[GraphQLFilter] = Field(
        default_factory=list,
        description="GraphQL filters to apply"
    )
    includes: Dict[str, bool] = Field(
        default_factory=dict,
        description="Additional data to include in query"
    )
    context: AgentContext = Field(
        default_factory=lambda: AgentContext(),
        description="Parsed context from the query"
    )
    requires_coordination: bool = Field(
        default=False,
        description="Whether this result needs coordination with other agents"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata for debugging/logging"
    )


class CombinedResult(BaseModel):
    """Combined result from multiple agents."""
    
    agent_results: List[AgentResult] = Field(
        ...,
        description="Results from individual agents"
    )
    final_filters: List[GraphQLFilter] = Field(
        ...,
        description="Combined and optimized GraphQL filters"
    )
    final_includes: Dict[str, bool] = Field(
        ...,
        description="Combined data includes"
    )
    query_explanation: str = Field(
        ...,
        description="Human-readable explanation of the final query"
    )
    confidence_scores: Dict[QueryType, float] = Field(
        ...,
        description="Confidence scores by agent type"
    )
    execution_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Metadata about query execution"
    )


class QueryIntent(BaseModel):
    """Structured representation of user query intent."""
    
    original_query: str = Field(
        ...,
        description="Original user query text"
    )
    detected_types: List[QueryType] = Field(
        ...,
        description="Types of queries detected in the input"
    )
    primary_type: QueryType = Field(
        ...,
        description="Primary query type with highest confidence"
    )
    combined_result: CombinedResult = Field(
        ...,
        description="Combined analysis from all relevant agents"
    )
    processing_time_ms: Optional[float] = Field(
        None,
        description="Time taken to process the query in milliseconds"
    )
    can_handle_directly: bool = Field(
        default=False,
        description="Whether the agents can provide a direct response without LLM"
    )
    direct_response: Optional[str] = Field(
        None,
        description="Direct response from agents if they can handle the query completely"
    )
    analysis_summary: Optional[str] = Field(
        None,
        description="Summary of agent analysis for LLM context"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata about the query processing"
    )


class AgentStatus(BaseModel):
    """Status information for an agent."""
    
    agent_type: QueryType = Field(
        ...,
        description="Type of the agent"
    )
    is_active: bool = Field(
        default=True,
        description="Whether the agent is currently active"
    )
    last_used: Optional[datetime] = Field(
        None,
        description="Timestamp of last usage"
    )
    total_queries: int = Field(
        default=0,
        description="Total number of queries processed"
    )
    average_confidence: float = Field(
        default=0.0,
        description="Average confidence score across queries"
    )
    error_count: int = Field(
        default=0,
        description="Number of errors encountered"
    )
